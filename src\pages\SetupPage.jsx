import React from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON><PERSON><PERSON><PERSON>, Settings } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import DatabaseInitializer from '../components/DatabaseInitializer'

const SetupPage = () => {
  const navigate = useNavigate()

  return (
    <div className="fuxion-page">
      {/* Decorative Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>

      {/* Header */}
      <div className="fuxion-header relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/')}
              className="text-blue-600 hover:text-blue-800"
            >
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <div>
              <h1 className="text-3xl font-black text-blue-600 tracking-wide">
                CONFIGURACIÓN FUXION
              </h1>
              <p className="text-blue-500 font-medium">Configura tu aplicación</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="fuxion-content relative z-10">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center mb-12">
            <div className="bg-white/90 backdrop-blur-lg rounded-3xl p-8 shadow-2xl">
              <Settings className="h-20 w-20 text-blue-600 mx-auto mb-6" />
              <h2 className="text-4xl font-black text-blue-600 mb-4 tracking-wide">
                CONFIGURACIÓN INICIAL
              </h2>
              <p className="text-blue-500 max-w-2xl mx-auto text-lg leading-relaxed">
                Bienvenido a FuXion E-commerce. Para comenzar a usar la aplicación,
                necesitas configurar Firebase y inicializar la base de datos con datos de ejemplo.
              </p>
            </div>
          </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Firebase Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  1
                </span>
                Configurar Firebase
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 text-sm">
                Antes de usar la aplicación, necesitas configurar Firebase:
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Crear un proyecto en Firebase Console
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Habilitar Authentication y Firestore
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Copiar la configuración a <code className="bg-gray-100 px-1 rounded">src/firebase-config.js</code>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Configurar Google Sign-In
                </li>
              </ul>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-xs">
                  <strong>Nota:</strong> Usa el archivo <code>firebase-config.example.js</code> como referencia.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Firestore Rules */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  2
                </span>
                Reglas de Firestore
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 text-sm">
                Configura las reglas de seguridad en Firebase Console:
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Ve a Firestore Database → Rules
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Copia el contenido de <code className="bg-gray-100 px-1 rounded">firestore.rules</code>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Publica las reglas
                </li>
              </ul>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-blue-800 text-xs">
                  <strong>Importante:</strong> Las reglas controlan el acceso a los datos y la seguridad.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Database Initializer */}
        <div className="mb-8">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center">
                <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  3
                </span>
                Inicializar Base de Datos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <DatabaseInitializer />
            </CardContent>
          </Card>
        </div>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle>Próximos Pasos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">🛍️</span>
                </div>
                <h3 className="font-semibold mb-2">Explorar Productos</h3>
                <p className="text-sm text-gray-600">
                  Navega por el catálogo de productos FuXion
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => navigate('/productos')}
                >
                  Ver Productos
                </Button>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">👤</span>
                </div>
                <h3 className="font-semibold mb-2">Crear Cuenta</h3>
                <p className="text-sm text-gray-600">
                  Inicia sesión para acceder a todas las funciones
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => navigate('/login')}
                >
                  Iniciar Sesión
                </Button>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">⚙️</span>
                </div>
                <h3 className="font-semibold mb-2">Panel Admin</h3>
                <p className="text-sm text-gray-600">
                  Accede al panel administrativo
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => navigate('/admin/login')}
                >
                  Admin Login
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SetupPage