import React, { useState } from 'react'
import { But<PERSON> } from './ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card'
import { Database, CheckCircle, AlertCircle } from 'lucide-react'
import { initializeFirestore } from '../utils/initializeFirestore'

const DatabaseInitializer = () => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)

  const handleInitialize = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await initializeFirestore()
      setResult(response)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="max-w-md mx-auto mt-8">
      <CardHeader className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Database className="h-8 w-8 text-blue-600" />
        </div>
        <CardTitle>Inicializar Base de Datos</CardTitle>
        <p className="text-gray-600 text-sm">
          Agrega datos de ejemplo a Firestore para probar la aplicación
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {result && (
          <div className={`p-4 rounded-lg flex items-center space-x-2 ${
            result.success 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {result.success ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <AlertCircle className="h-5 w-5" />
            )}
            <span className="text-sm">
              {result.success ? result.message : `Error: ${result.error}`}
            </span>
          </div>
        )}

        <Button
          onClick={handleInitialize}
          disabled={loading}
          className="w-full"
          size="lg"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Inicializando...
            </>
          ) : (
            'Inicializar Base de Datos'
          )}
        </Button>

        <div className="text-xs text-gray-500 text-center">
          <p>Esto agregará:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>6 productos de ejemplo</li>
            <li>2 órdenes de ejemplo</li>
            <li>3 usuarios de ejemplo</li>
            <li>2 direcciones de ejemplo</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

export default DatabaseInitializer