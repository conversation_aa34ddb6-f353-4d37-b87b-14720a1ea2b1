import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useCart } from '../context/CartContext'

const CartPage = () => {
  const navigate = useNavigate()
  const { items, updateQuantity, removeItem, getCartTotal, getCartItemsCount } = useCart()

  const handleQuantityChange = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(productId)
    } else {
      updateQuantity(productId, newQuantity)
    }
  }

  const handleCheckout = () => {
    navigate('/checkout')
  }

  if (items.length === 0) {
    return (
      <div className="fuxion-page">
        {/* Decorative Background Elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>

        {/* Header */}
        <div className="fuxion-header relative z-10">
          <div className="max-w-7xl mx-auto px-4 py-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/productos')}
                className="text-blue-600 hover:text-blue-800"
              >
                <ArrowLeft className="h-6 w-6" />
              </Button>
              <div>
                <h1 className="text-3xl font-black text-blue-600 tracking-wide">
                  CARRITO DE COMPRAS
                </h1>
                <p className="text-blue-500 font-medium">Tu selección de productos FuXion</p>
              </div>
            </div>
          </div>
        </div>

        {/* Empty Cart */}
        <div className="fuxion-content relative z-10">
          <div className="max-w-2xl mx-auto px-4 py-16 text-center">
            <div className="bg-white/90 backdrop-blur-lg rounded-3xl p-12 shadow-2xl">
              <ShoppingBag className="h-32 w-32 text-blue-300 mx-auto mb-8" />
              <h2 className="text-3xl font-black text-blue-600 mb-4 tracking-wide">
                TU CARRITO ESTÁ VACÍO
              </h2>
              <p className="text-blue-500 mb-8 text-lg">
                Agrega algunos productos para comenzar tu compra
              </p>
              <Button
                variant="primary"
                onClick={() => navigate('/productos')}
                className="px-8 py-4 text-lg font-bold"
              >
                CONTINUAR COMPRANDO
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fuxion-page">
      {/* Decorative Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>

      {/* Header */}
      <div className="fuxion-header relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/productos')}
                className="text-blue-600 hover:text-blue-800"
              >
                <ArrowLeft className="h-6 w-6" />
              </Button>
              <div>
                <h1 className="text-3xl font-black text-blue-600 tracking-wide">
                  CARRITO DE COMPRAS
                </h1>
                <p className="text-blue-500 font-medium">Tu selección de productos FuXion</p>
              </div>
            </div>
            <div className="text-blue-600 font-bold">
              {getCartItemsCount()} {getCartItemsCount() === 1 ? 'artículo' : 'artículos'}
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="fuxion-content relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-6">
              {items.map((item) => (
                <div key={item.id} className="card">
                  <div className="p-6">
                    <div className="flex items-center space-x-6">
                      {/* Product Image */}
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl overflow-hidden flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.src = `https://via.placeholder.com/96x96/e5e7eb/6b7280?text=${encodeURIComponent(item.name.substring(0, 2))}`
                          }}
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-xl font-black text-gray-800 truncate tracking-wide">
                          {item.name}
                        </h3>
                        <p className="text-sm text-gray-600 mt-2 leading-relaxed">
                          {item.description}
                        </p>
                        <p className="text-2xl font-black text-blue-600 mt-3">
                          ${item.price.toFixed(2)}
                      </p>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      
                      <span className="text-lg font-semibold min-w-[2rem] text-center">
                        {item.quantity}
                      </span>
                      
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Item Total */}
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">
                        ${(item.price * item.quantity).toFixed(2)}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 mt-2"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Eliminar
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle>Resumen del Pedido</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>${getCartTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Envío</span>
                    <span>Calculado en checkout</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Impuestos</span>
                    <span>Calculado en checkout</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Total</span>
                      <span>${getCartTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handleCheckout}
                >
                  Proceder al Pago
                </Button>

                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => navigate('/productos')}
                >
                  Continuar Comprando
                </Button>

                {/* Security Info */}
                <div className="text-xs text-gray-500 text-center pt-4 border-t">
                  <p>🔒 Compra segura con encriptación SSL</p>
                  <p>📦 Envío gratuito en pedidos mayores a $100</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CartPage