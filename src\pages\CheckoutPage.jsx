import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, CreditCard, MapPin, Package } from 'lucide-react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Input } from '../components/ui/Input'
import { useCart } from '../context/CartContext'
import { useAuth } from '../context/AuthContext'
import { apiService } from '../services/api'

// Initialize Stripe
const stripePromise = loadStripe('pk_test_your_publishable_key_here')

const CheckoutForm = ({ orderData, onSuccess }) => {
  const stripe = useStripe()
  const elements = useElements()
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (event) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setProcessing(true)
    setError('')

    try {
      // Create payment intent
      const { clientSecret } = await apiService.createPaymentIntent(
        Math.round(orderData.total * 100) // Convert to cents
      )

      // Confirm payment
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement),
          billing_details: {
            name: orderData.shippingAddress.fullName,
            email: orderData.customerEmail,
            address: {
              line1: orderData.shippingAddress.address,
              city: orderData.shippingAddress.city,
              state: orderData.shippingAddress.state,
              postal_code: orderData.shippingAddress.zipCode,
              country: orderData.shippingAddress.country,
            },
          },
        },
      })

      if (result.error) {
        setError(result.error.message)
      } else {
        // Payment succeeded
        onSuccess(result.paymentIntent)
      }
    } catch (err) {
      setError('Error procesando el pago. Por favor, intenta de nuevo.')
      console.error('Payment error:', err)
    } finally {
      setProcessing(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Información de Pago
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 border rounded-lg">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                },
              }}
            />
          </div>
        </CardContent>
      </Card>

      <Button
        type="submit"
        disabled={!stripe || processing}
        className="w-full"
        size="lg"
      >
        {processing ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            Procesando...
          </>
        ) : (
          `Pagar $${orderData.total.toFixed(2)}`
        )}
      </Button>
    </form>
  )
}

const CheckoutPage = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { items, getCartTotal, clearCart } = useCart()
  const { user } = useAuth()
  
  const [step, setStep] = useState(1)
  const [shippingAddress, setShippingAddress] = useState({
    fullName: user?.displayName || '',
    email: user?.email || '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US'
  })
  const [savedAddresses, setSavedAddresses] = useState([])
  const [selectedAddressId, setSelectedAddressId] = useState('')
  const [loading, setLoading] = useState(false)

  const isGuest = location.state?.isGuest || false

  useEffect(() => {
    if (items.length === 0) {
      navigate('/carrito')
      return
    }

    // Load saved addresses if user is authenticated
    if (user && !isGuest) {
      loadSavedAddresses()
    }
  }, [items, user, isGuest, navigate])

  const loadSavedAddresses = async () => {
    try {
      const addresses = await apiService.getUserAddresses(user.uid)
      setSavedAddresses(addresses)
    } catch (error) {
      console.error('Error loading addresses:', error)
    }
  }

  const handleAddressChange = (field, value) => {
    setShippingAddress(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSavedAddressSelect = (addressId) => {
    const address = savedAddresses.find(addr => addr.id === addressId)
    if (address) {
      setShippingAddress(address)
      setSelectedAddressId(addressId)
    }
  }

  const handlePaymentSuccess = async (paymentIntent) => {
    try {
      setLoading(true)
      
      // Create order
      const orderData = {
        items: items.map(item => ({
          productId: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity
        })),
        shippingAddress,
        total: getCartTotal(),
        paymentIntentId: paymentIntent.id,
        customerEmail: shippingAddress.email,
        customerId: user?.uid || null
      }

      await apiService.createOrder(orderData)
      
      // Clear cart
      clearCart()
      
      // Navigate to success page
      navigate('/checkout/success', { 
        state: { 
          orderId: paymentIntent.id,
          total: getCartTotal()
        }
      })
    } catch (error) {
      console.error('Error creating order:', error)
      // Handle error
    } finally {
      setLoading(false)
    }
  }

  const orderData = {
    items,
    shippingAddress,
    total: getCartTotal(),
    customerEmail: shippingAddress.email
  }

  return (
    <div className="fuxion-page">
      {/* Decorative Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>

      {/* Header */}
      <div className="fuxion-header relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/carrito')}
              className="text-blue-600 hover:text-blue-800"
            >
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <div>
              <h1 className="text-3xl font-black text-blue-600 tracking-wide">
                CHECKOUT
              </h1>
              <p className="text-blue-500 font-medium">Finaliza tu compra FuXion</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Checkout Form */}
          <div className="space-y-6">
            {/* Step 1: Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Dirección de Envío
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Saved Addresses */}
                {savedAddresses.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Direcciones Guardadas</label>
                    <select
                      value={selectedAddressId}
                      onChange={(e) => handleSavedAddressSelect(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Seleccionar dirección guardada</option>
                      {savedAddresses.map((address) => (
                        <option key={address.id} value={address.id}>
                          {address.fullName} - {address.address}, {address.city}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    placeholder="Nombre completo"
                    value={shippingAddress.fullName}
                    onChange={(e) => handleAddressChange('fullName', e.target.value)}
                    required
                  />
                  <Input
                    type="email"
                    placeholder="Email"
                    value={shippingAddress.email}
                    onChange={(e) => handleAddressChange('email', e.target.value)}
                    required
                  />
                </div>

                <Input
                  type="tel"
                  placeholder="Teléfono"
                  value={shippingAddress.phone}
                  onChange={(e) => handleAddressChange('phone', e.target.value)}
                  required
                />

                <Input
                  placeholder="Dirección"
                  value={shippingAddress.address}
                  onChange={(e) => handleAddressChange('address', e.target.value)}
                  required
                />

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    placeholder="Ciudad"
                    value={shippingAddress.city}
                    onChange={(e) => handleAddressChange('city', e.target.value)}
                    required
                  />
                  <Input
                    placeholder="Estado"
                    value={shippingAddress.state}
                    onChange={(e) => handleAddressChange('state', e.target.value)}
                    required
                  />
                </div>

                <Input
                  placeholder="Código Postal"
                  value={shippingAddress.zipCode}
                  onChange={(e) => handleAddressChange('zipCode', e.target.value)}
                  required
                />
              </CardContent>
            </Card>

            {/* Step 2: Payment */}
            <Elements stripe={stripePromise}>
              <CheckoutForm 
                orderData={orderData}
                onSuccess={handlePaymentSuccess}
              />
            </Elements>
          </div>

          {/* Order Summary */}
          <div>
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Resumen del Pedido
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Items */}
                <div className="space-y-3">
                  {items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center">
                      <div className="flex-1">
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-600">
                          Cantidad: {item.quantity}
                        </p>
                      </div>
                      <p className="font-semibold">
                        ${(item.price * item.quantity).toFixed(2)}
                      </p>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${getCartTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Envío</span>
                    <span>Gratis</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Impuestos</span>
                    <span>$0.00</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>${getCartTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CheckoutPage