@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    color: #111827;
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.5;
    min-height: 100vh;
  }

  #root {
    min-height: 100vh;
  }
}

@layer components {
  /* FuXion Brand Colors */
  .fuxion-bg-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
  }

  .fuxion-bg-secondary {
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 50%, #4169E1 100%);
  }

  /* Page Layout Base */
  .fuxion-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    position: relative;
    overflow-x: hidden;
  }

  .fuxion-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, transparent 100%);
    pointer-events: none;
  }

  .fuxion-page::after {
    content: '';
    position: absolute;
    top: -200px;
    right: -200px;
    width: 400px;
    height: 400px;
    background: rgba(255,255,255,0.05);
    border-radius: 50%;
    pointer-events: none;
  }

  /* Content Container */
  .fuxion-content {
    position: relative;
    z-index: 10;
    background: rgba(255,255,255,0.95);
    border-radius: 20px 20px 0 0;
    margin-top: 2rem;
    min-height: calc(100vh - 2rem);
    box-shadow: 0 -10px 30px rgba(0,0,0,0.1);
  }

  /* Header Styles */
  .fuxion-header {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
  }

  /* Navigation Styles */
  .fuxion-nav {
    background: rgba(255,255,255,0.98);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(30,144,255,0.1);
    box-shadow: 0 2px 20px rgba(30,144,255,0.1);
  }

  /* Button Styles */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    outline: none;
    cursor: pointer;
    border: none;
    padding: 0.75rem 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .btn:focus-visible {
    outline: 2px solid #1E90FF;
    outline-offset: 2px;
  }

  .btn:disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  .btn-primary {
    background: linear-gradient(135deg, #1E90FF 0%, #4169E1 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(30,144,255,0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #4169E1 0%, #0000CD 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30,144,255,0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1E90FF;
    border: 2px solid #1E90FF;
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #1E90FF 0%, #4169E1 100%);
    color: white;
    transform: translateY(-2px);
  }

  .btn-outline {
    background: rgba(255,255,255,0.9);
    color: #1E90FF;
    border: 2px solid #1E90FF;
    backdrop-filter: blur(10px);
  }

  .btn-outline:hover {
    background: #1E90FF;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(30,144,255,0.3);
  }

  /* Card Styles */
  .card {
    border-radius: 16px;
    border: 1px solid rgba(30,144,255,0.1);
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(30,144,255,0.1);
    transition: all 0.3s ease;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(30,144,255,0.2);
  }

  /* Product Card Styles */
  .product-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(30,144,255,0.2);
    border-color: #1E90FF;
  }

  /* Health Need Card (Homepage style) */
  .health-need-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .health-need-card:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
  }

  /* Input Styles */
  .fuxion-input {
    border: 2px solid rgba(30,144,255,0.2);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .fuxion-input:focus {
    border-color: #1E90FF;
    box-shadow: 0 0 0 3px rgba(30,144,255,0.1);
    outline: none;
  }
}
