import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { LogOut, Search, Filter, Eye, Package, Clock, CheckCircle, Truck } from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Input } from '../../components/ui/Input'
import { apiService } from '../../services/api'

const AdminDashboard = () => {
  const navigate = useNavigate()
  const [orders, setOrders] = useState([])
  const [filteredOrders, setFilteredOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')

  const orderStatuses = [
    { id: 'all', name: 'Todos', color: 'gray' },
    { id: 'pending', name: 'Pendie<PERSON>', color: 'yellow', icon: Clock },
    { id: 'paid', name: 'Pagado', color: 'blue', icon: CheckCircle },
    { id: 'shipped', name: 'Enviado', color: 'green', icon: Truck },
    { id: 'delivered', name: 'Entregado', color: 'green', icon: Package }
  ]

  // Mock orders data - replace with API call
  const mockOrders = [
    {
      id: 'ORD-001',
      customerName: 'Juan Pérez',
      customerEmail: '<EMAIL>',
      total: 125.99,
      status: 'paid',
      createdAt: '2024-01-15T10:30:00Z',
      items: [
        { name: 'THERMO T5', quantity: 2, price: 68.99 },
        { name: 'VITA XTRA+', quantity: 1, price: 42.99 }
      ],
      shippingAddress: {
        fullName: 'Juan Pérez',
        address: '123 Main St',
        city: 'Miami',
        state: 'FL',
        zipCode: '33101'
      }
    },
    {
      id: 'ORD-002',
      customerName: 'María García',
      customerEmail: '<EMAIL>',
      total: 75.99,
      status: 'shipped',
      createdAt: '2024-01-14T15:45:00Z',
      trackingNumber: 'TRK123456789',
      items: [
        { name: 'BEAUTY-IN', quantity: 1, price: 75.99 }
      ],
      shippingAddress: {
        fullName: 'María García',
        address: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210'
      }
    },
    {
      id: 'ORD-003',
      customerName: 'Carlos López',
      customerEmail: '<EMAIL>',
      total: 91.98,
      status: 'pending',
      createdAt: '2024-01-16T09:15:00Z',
      items: [
        { name: 'PRUNEX 1', quantity: 2, price: 45.99 }
      ],
      shippingAddress: {
        fullName: 'Carlos López',
        address: '789 Pine St',
        city: 'Houston',
        state: 'TX',
        zipCode: '77001'
      }
    }
  ]

  useEffect(() => {
    loadOrders()
  }, [])

  useEffect(() => {
    filterOrders()
  }, [orders, searchTerm, statusFilter, dateFilter])

  const loadOrders = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('admin-token')
      
      if (!token) {
        navigate('/admin/login')
        return
      }

      // Try to get orders from Firestore
      const ordersData = await apiService.getOrders()
      setOrders(ordersData.length > 0 ? ordersData : mockOrders)
    } catch (error) {
      console.error('Error loading orders:', error)
      // Fallback to mock data if Firestore fails
      setOrders(mockOrders)
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = orders

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Filter by date
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          filtered = filtered.filter(order => new Date(order.createdAt) >= filterDate)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          filtered = filtered.filter(order => new Date(order.createdAt) >= filterDate)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          filtered = filtered.filter(order => new Date(order.createdAt) >= filterDate)
          break
      }
    }

    setFilteredOrders(filtered)
  }

  const handleLogout = () => {
    localStorage.removeItem('admin-token')
    localStorage.removeItem('admin-user')
    navigate('/admin/login')
  }

  const getStatusColor = (status) => {
    const statusConfig = orderStatuses.find(s => s.id === status)
    return statusConfig?.color || 'gray'
  }

  const getStatusIcon = (status) => {
    const statusConfig = orderStatuses.find(s => s.id === status)
    return statusConfig?.icon || Package
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando órdenes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Panel de Administración
              </h1>
              <p className="text-gray-600">Gestión de órdenes FuXion</p>
            </div>
            <Button
              variant="outline"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Cerrar Sesión
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          {orderStatuses.slice(1).map((status) => {
            const count = orders.filter(order => order.status === status.id).length
            const Icon = status.icon
            return (
              <Card key={status.id}>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg bg-${status.color}-100`}>
                      <Icon className={`h-6 w-6 text-${status.color}-600`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">
                        {status.name}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {count}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por ID, cliente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {orderStatuses.map((status) => (
                  <option key={status.id} value={status.id}>
                    {status.name}
                  </option>
                ))}
              </select>

              {/* Date Filter */}
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todas las fechas</option>
                <option value="today">Hoy</option>
                <option value="week">Última semana</option>
                <option value="month">Último mes</option>
              </select>

              {/* Clear Filters */}
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setStatusFilter('all')
                  setDateFilter('all')
                }}
              >
                <Filter className="h-4 w-4 mr-2" />
                Limpiar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>
              Órdenes ({filteredOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      ID Orden
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      Cliente
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      Total
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      Estado
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      Fecha
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => {
                    const StatusIcon = getStatusIcon(order.status)
                    const statusColor = getStatusColor(order.status)
                    
                    return (
                      <tr key={order.id} className="border-b hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium">
                          {order.id}
                        </td>
                        <td className="py-4 px-4">
                          <div>
                            <p className="font-medium">{order.customerName}</p>
                            <p className="text-sm text-gray-600">{order.customerEmail}</p>
                          </div>
                        </td>
                        <td className="py-4 px-4 font-semibold">
                          ${order.total.toFixed(2)}
                        </td>
                        <td className="py-4 px-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusColor}-100 text-${statusColor}-800`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {orderStatuses.find(s => s.id === order.status)?.name}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-sm text-gray-600">
                          {formatDate(order.createdAt)}
                        </td>
                        <td className="py-4 px-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/admin/orden/${order.id}`)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Ver
                          </Button>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>

              {filteredOrders.length === 0 && (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No se encontraron órdenes</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default AdminDashboard