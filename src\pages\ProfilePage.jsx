import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, User, MapPin, Package, Plus, Edit, Trash2 } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Input } from '../components/ui/Input'
import { useAuth } from '../context/AuthContext'
import { apiService } from '../services/api'

const ProfilePage = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  const [activeTab, setActiveTab] = useState('profile')
  const [addresses, setAddresses] = useState([])
  const [editingAddress, setEditingAddress] = useState(null)
  const [showAddressForm, setShowAddressForm] = useState(false)
  const [loading, setLoading] = useState(false)
  
  const [addressForm, setAddressForm] = useState({
    fullName: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    isDefault: false
  })

  useEffect(() => {
    if (activeTab === 'addresses') {
      loadAddresses()
    }
  }, [activeTab])

  const loadAddresses = async () => {
    try {
      setLoading(true)
      const userAddresses = await apiService.getUserAddresses(user.uid)
      setAddresses(userAddresses)
    } catch (error) {
      console.error('Error loading addresses:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddressSubmit = async (e) => {
    e.preventDefault()
    try {
      setLoading(true)
      
      if (editingAddress) {
        await apiService.updateUserAddress(editingAddress.id, addressForm)
      } else {
        await apiService.saveUserAddress(addressForm, user.uid)
      }
      
      await loadAddresses()
      setShowAddressForm(false)
      setEditingAddress(null)
      resetAddressForm()
    } catch (error) {
      console.error('Error saving address:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEditAddress = (address) => {
    setAddressForm(address)
    setEditingAddress(address)
    setShowAddressForm(true)
  }

  const handleDeleteAddress = async (addressId) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta dirección?')) {
      try {
        setLoading(true)
        await apiService.deleteUserAddress(addressId)
        await loadAddresses()
      } catch (error) {
        console.error('Error deleting address:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  const resetAddressForm = () => {
    setAddressForm({
      fullName: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
      isDefault: false
    })
  }

  const handleAddressFormChange = (field, value) => {
    setAddressForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const tabs = [
    { id: 'profile', name: 'Perfil', icon: User },
    { id: 'addresses', name: 'Direcciones', icon: MapPin },
    { id: 'orders', name: 'Pedidos', icon: Package }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">
              Mi Perfil
            </h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="md:col-span-1">
            <Card>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center px-4 py-3 text-left hover:bg-gray-50 ${
                          activeTab === tab.id
                            ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600'
                            : 'text-gray-700'
                        }`}
                      >
                        <Icon className="h-5 w-5 mr-3" />
                        {tab.name}
                      </button>
                    )
                  })}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Content */}
          <div className="md:col-span-3">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle>Información del Perfil</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                      {user?.photoURL ? (
                        <img
                          src={user.photoURL}
                          alt="Profile"
                          className="w-16 h-16 rounded-full"
                        />
                      ) : (
                        <User className="h-8 w-8 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">
                        {user?.displayName || 'Usuario'}
                      </h3>
                      <p className="text-gray-600">{user?.email}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nombre
                      </label>
                      <Input
                        value={user?.displayName || ''}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <Input
                        value={user?.email || ''}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    <p>Cuenta creada: {user?.metadata?.creationTime}</p>
                    <p>Último acceso: {user?.metadata?.lastSignInTime}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Addresses Tab */}
            {activeTab === 'addresses' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Mis Direcciones</h2>
                  <Button
                    onClick={() => {
                      resetAddressForm()
                      setShowAddressForm(true)
                      setEditingAddress(null)
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Agregar Dirección
                  </Button>
                </div>

                {/* Address Form */}
                {showAddressForm && (
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {editingAddress ? 'Editar Dirección' : 'Nueva Dirección'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleAddressSubmit} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Input
                            placeholder="Nombre completo"
                            value={addressForm.fullName}
                            onChange={(e) => handleAddressFormChange('fullName', e.target.value)}
                            required
                          />
                          <Input
                            type="tel"
                            placeholder="Teléfono"
                            value={addressForm.phone}
                            onChange={(e) => handleAddressFormChange('phone', e.target.value)}
                            required
                          />
                        </div>

                        <Input
                          placeholder="Dirección"
                          value={addressForm.address}
                          onChange={(e) => handleAddressFormChange('address', e.target.value)}
                          required
                        />

                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            placeholder="Ciudad"
                            value={addressForm.city}
                            onChange={(e) => handleAddressFormChange('city', e.target.value)}
                            required
                          />
                          <Input
                            placeholder="Estado"
                            value={addressForm.state}
                            onChange={(e) => handleAddressFormChange('state', e.target.value)}
                            required
                          />
                        </div>

                        <Input
                          placeholder="Código Postal"
                          value={addressForm.zipCode}
                          onChange={(e) => handleAddressFormChange('zipCode', e.target.value)}
                          required
                        />

                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="isDefault"
                            checked={addressForm.isDefault}
                            onChange={(e) => handleAddressFormChange('isDefault', e.target.checked)}
                          />
                          <label htmlFor="isDefault" className="text-sm">
                            Establecer como dirección predeterminada
                          </label>
                        </div>

                        <div className="flex space-x-2">
                          <Button type="submit" disabled={loading}>
                            {loading ? 'Guardando...' : 'Guardar'}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setShowAddressForm(false)
                              setEditingAddress(null)
                            }}
                          >
                            Cancelar
                          </Button>
                        </div>
                      </form>
                    </CardContent>
                  </Card>
                )}

                {/* Address List */}
                <div className="space-y-4">
                  {addresses.map((address) => (
                    <Card key={address.id}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-semibold">{address.fullName}</h3>
                            <p className="text-gray-600">{address.phone}</p>
                            <p className="text-gray-600">
                              {address.address}
                            </p>
                            <p className="text-gray-600">
                              {address.city}, {address.state} {address.zipCode}
                            </p>
                            {address.isDefault && (
                              <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mt-2">
                                Predeterminada
                              </span>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAddress(address)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAddress(address.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {addresses.length === 0 && !loading && (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-600">No tienes direcciones guardadas</p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {/* Orders Tab */}
            {activeTab === 'orders' && (
              <Card>
                <CardHeader>
                  <CardTitle>Mis Pedidos</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">
                      El historial de pedidos estará disponible próximamente
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage