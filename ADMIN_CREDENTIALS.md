# Credenciales de Administrador

## Acceso al Panel Administrativo

Para acceder al panel administrativo de FuXion E-commerce, utiliza las siguientes credenciales:

### URL de Acceso
```
http://localhost:5173/admin/login
```

### Credenciales por Defecto
- **Email**: `<EMAIL>`
- **Contraseña**: `admin123`

## Funcionalidades del Panel Admin

Una vez autenticado como administrador, tendrás acceso a:

1. **Dashboard de Órdenes** (`/admin/ordenes`)
   - Vista general de todas las órdenes
   - Filtros por estado y fecha
   - Estadísticas de órdenes

2. **Detalle de Órdenes** (`/admin/orden/:id`)
   - Información completa de cada orden
   - Actualización de estados
   - Gestión de números de seguimiento

## Estados de Órdenes

Los estados disponibles son:
- **Pendiente**: Orden creada pero no procesada
- **Pagado**: Pago confirmado
- **Enviado**: Orden enviada al cliente
- **Entregado**: Orden entregada exitosamente

## Notas de Seguridad

⚠️ **IMPORTANTE**: En un entorno de producción:

1. Cambia las credenciales por defecto
2. Implementa autenticación real con Firebase Auth Custom Claims
3. Usa variables de entorno para credenciales sensibles
4. Configura roles y permisos apropiados en Firestore Rules

## Configuración de Administradores Reales

Para configurar administradores reales en Firebase:

1. Ve a Firebase Console → Authentication
2. Crea usuarios administradores
3. Usa Firebase Admin SDK para asignar custom claims:

```javascript
// En Cloud Functions
const admin = require('firebase-admin');

await admin.auth().setCustomUserClaims(uid, { isAdmin: true });
```

4. Actualiza las reglas de Firestore para usar custom claims:

```javascript
// En firestore.rules
allow read, write: if request.auth != null && 
  request.auth.token.isAdmin == true;
```