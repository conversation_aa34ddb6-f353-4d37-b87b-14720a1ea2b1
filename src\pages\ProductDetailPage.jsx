import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Star, ShoppingCart } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useCart } from '../context/CartContext'
import { apiService } from '../services/api'

const ProductDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { addItem } = useCart()
  
  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)

  // Mock product data - replace with API call
  const mockProduct = {
    id: parseInt(id),
    name: 'THERMO T5',
    category: 'perdida-peso',
    price: 68.99,
    images: [
      '/products/thermo-t5-1.jpg',
      '/products/thermo-t5-2.jpg',
      '/products/thermo-t5-3.jpg'
    ],
    description: 'THERMO T5 es un suplemento natural diseñado para acelerar el metabolismo y ayudar en la quema de grasa de manera efectiva y segura.',
    longDescription: `
      THERMO T5 combina ingredientes naturales cuidadosamente seleccionados para proporcionar un impulso metabólico que te ayuda a alcanzar tus objetivos de pérdida de peso.
      
      Este producto ha sido formulado con extractos de plantas y compuestos naturales que trabajan en sinergia para:
      - Acelerar el metabolismo basal
      - Aumentar la termogénesis
      - Reducir la sensación de hambre
      - Proporcionar energía sostenida
    `,
    usage: `
      Modo de uso:
      - Tomar 1 cápsula 30 minutos antes del desayuno
      - Tomar 1 cápsula 30 minutos antes del almuerzo
      - No exceder 2 cápsulas por día
      - Acompañar con abundante agua
      - No tomar después de las 6 PM para evitar insomnio
    `,
    ingredients: [
      'Extracto de Té Verde',
      'L-Carnitina',
      'Cafeína Natural',
      'Extracto de Garcinia Cambogia',
      'Cromo Picolinato',
      'Extracto de Guaraná'
    ],
    benefits: [
      'Acelera el metabolismo',
      'Quema grasa corporal',
      'Aumenta la energía',
      'Reduce el apetito',
      'Mejora el rendimiento físico'
    ],
    rating: 4.5,
    reviewsCount: 127,
    inStock: true,
    stock: 15
  }

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true)
        // Try to get product from Firestore
        const data = await apiService.getProduct(id)
        setProduct(data)
      } catch (error) {
        console.error('Error fetching product:', error)
        // Fallback to mock data if Firestore fails
        setProduct(mockProduct)
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [id])

  const handleAddToCart = () => {
    if (product) {
      addItem(product)
      // You could add a toast notification here
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando producto...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 text-lg">Producto no encontrado</p>
          <Button onClick={() => navigate('/productos')} className="mt-4">
            Volver a productos
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/productos')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">
              Detalle del Producto
            </h1>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square bg-white rounded-lg overflow-hidden shadow-sm">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = `https://via.placeholder.com/600x600/e5e7eb/6b7280?text=${encodeURIComponent(product.name)}`
                }}
              />
            </div>
            
            {product.images.length > 1 && (
              <div className="flex space-x-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-blue-500' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = `https://via.placeholder.com/80x80/e5e7eb/6b7280?text=${index + 1}`
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {product.name}
              </h1>
              
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-600">
                  {product.rating} ({product.reviewsCount} reseñas)
                </span>
              </div>

              <p className="text-4xl font-bold text-blue-600 mb-4">
                ${product.price.toFixed(2)}
              </p>

              <p className="text-gray-700 mb-6">
                {product.description}
              </p>
            </div>

            {/* Add to Cart */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded text-sm ${
                  product.inStock 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.inStock ? `En stock (${product.stock} disponibles)` : 'Agotado'}
                </span>
              </div>

              <Button
                size="lg"
                className="w-full"
                onClick={handleAddToCart}
                disabled={!product.inStock}
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                Añadir al Carrito
              </Button>
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Descripción Detallada</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                {product.longDescription.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-3">
                    {paragraph.trim()}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Usage Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Modo de Uso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                {product.usage.split('\n').map((line, index) => (
                  <p key={index} className="mb-2">
                    {line.trim()}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Ingredients */}
          <Card>
            <CardHeader>
              <CardTitle>Ingredientes</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-1">
                {product.ingredients.map((ingredient, index) => (
                  <li key={index} className="text-gray-700">
                    {ingredient}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Benefits */}
          <Card>
            <CardHeader>
              <CardTitle>Beneficios</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-1">
                {product.benefits.map((benefit, index) => (
                  <li key={index} className="text-gray-700">
                    {benefit}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Reviews Section Placeholder */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Reseñas de Clientes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-8">
              Las reseñas estarán disponibles próximamente
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ProductDetailPage