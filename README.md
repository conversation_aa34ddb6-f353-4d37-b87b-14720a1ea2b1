# FuXion E-commerce App

Una aplicación completa de comercio electrónico para productos de salud FuXion, construida con React, Vite, Tailwind CSS, Radix UI, Firebase y Capacitor.

## 🚀 Características

### Frontend para Clientes
- **Página Principal**: Diseño basado en necesidades de salud con navegación intuitiva
- **Catálogo de Productos**: Listado con filtros por categoría y búsqueda
- **Detalle de Producto**: Información completa, imágenes, ingredientes y modo de uso
- **Carrito de Compras**: Gestión de productos con cantidades y totales
- **Autenticación**: Login con Google Firebase Auth
- **Perfil de Usuario**: Gestión de direcciones y datos personales
- **Checkout**: Proceso de pago integrado con Stripe

### Panel Administrativo
- **Dashboard de Órdenes**: Vista general con filtros y estadísticas
- **Gestión de Órdenes**: Actualización de estados y números de seguimiento
- **Detalle de Órdenes**: Información completa de cada pedido

### Tecnologías
- **Frontend**: React 18, Vite, Tailwind CSS, Radix UI
- **Routing**: React Router DOM
- **Pagos**: Stripe Integration
- **Autenticación**: Firebase Authentication
- **Base de Datos**: Firestore (acceso directo)
- **Mobile**: Capacitor para iOS y Android
- **Estado**: Context API para carrito y autenticación

## 📱 Estructura del Proyecto

```
src/
├── components/
│   ├── ui/                 # Componentes UI reutilizables
│   ├── Navigation.jsx      # Navegación principal
│   └── ProtectedRoute.jsx  # Rutas protegidas
├── context/
│   ├── AuthContext.jsx     # Contexto de autenticación
│   └── CartContext.jsx     # Contexto del carrito
├── pages/
│   ├── HomePage.jsx        # Página principal
│   ├── ProductsPage.jsx    # Listado de productos
│   ├── ProductDetailPage.jsx # Detalle del producto
│   ├── CartPage.jsx        # Carrito de compras
│   ├── CheckoutPage.jsx    # Proceso de pago
│   ├── LoginPage.jsx       # Autenticación
│   ├── ProfilePage.jsx     # Perfil de usuario
│   └── admin/              # Páginas administrativas
├── services/
│   └── api.js              # Servicios para Cloud Functions
└── lib/
    └── utils.js            # Utilidades
```

## 🛠️ Instalación y Configuración

### Prerrequisitos
- Node.js 18+
- npm o yarn
- Android Studio (para desarrollo Android)
- Xcode (para desarrollo iOS, solo macOS)

### Instalación
```bash
# Clonar el repositorio
git clone <repository-url>
cd fuxion-ecommerce

# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev
```

### Configuración de Firebase

#### 1. Crear Proyecto Firebase
1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Crea un nuevo proyecto
3. Habilita Google Analytics (opcional)

#### 2. Configurar Authentication
1. Ve a Authentication → Sign-in method
2. Habilita "Google" como proveedor
3. Configura el dominio autorizado para tu aplicación

#### 3. Configurar Firestore
1. Ve a Firestore Database
2. Crea una base de datos en modo "test" inicialmente
3. Selecciona una ubicación cercana a tus usuarios

#### 4. Configurar las Reglas de Firestore
1. Ve a Firestore Database → Rules
2. Copia y pega el contenido del archivo `firestore.rules`
3. Publica las reglas

#### 5. Obtener Configuración del Proyecto
1. Ve a Project Settings (⚙️)
2. En la sección "Your apps", selecciona Web
3. Copia la configuración de Firebase
4. Crea el archivo `src/firebase-config.js` con tu configuración:

```javascript
import { initializeApp } from 'firebase/app'
import { getAuth, GoogleAuthProvider } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

const firebaseConfig = {
  apiKey: "tu-api-key",
  authDomain: "tu-proyecto.firebaseapp.com",
  projectId: "tu-proyecto-id",
  storageBucket: "tu-proyecto.appspot.com",
  messagingSenderId: "123456789",
  appId: "tu-app-id"
}

const app = initializeApp(firebaseConfig)
export const auth = getAuth(app)
export const googleProvider = new GoogleAuthProvider()
export const db = getFirestore(app)
export default app
```

#### 6. Inicializar Base de Datos
1. Ejecuta la aplicación: `npm run dev`
2. Ve a `/setup` en tu navegador
3. Haz clic en "Inicializar Base de Datos" para agregar datos de ejemplo

### Configuración de Stripe
1. Crear cuenta en [Stripe](https://stripe.com)
2. Obtener claves públicas y privadas del dashboard
3. Actualizar la clave pública en `src/pages/CheckoutPage.jsx`:
```javascript
const stripePromise = loadStripe('pk_test_tu_clave_publica_aqui')
```

### Colecciones de Firestore
La aplicación utiliza las siguientes colecciones:

- **`products`**: Productos del catálogo
- **`orders`**: Órdenes de compra
- **`users`**: Información de usuarios
- **`userAddresses`**: Direcciones de envío de usuarios

## 📱 Desarrollo Mobile con Capacitor

### Build para producción
```bash
npm run build
```

### Sincronizar con Capacitor
```bash
npx cap sync
```

### Ejecutar en Android
```bash
npx cap run android
```

### Ejecutar en iOS
```bash
npx cap run ios
```

## 🎨 Personalización

### Colores de Marca
Los colores de FuXion están definidos en `tailwind.config.js`:
```javascript
colors: {
  fuxion: {
    blue: '#0066CC',
    green: '#00CC66',
    orange: '#FF6600',
    purple: '#6600CC',
    red: '#CC0066',
    yellow: '#CCCC00',
  }
}
```

### Categorías de Productos
Las categorías están definidas en `src/pages/HomePage.jsx` y pueden ser modificadas según tus necesidades.

## 🔧 Scripts Disponibles

- `npm run dev` - Ejecutar en modo desarrollo
- `npm run build` - Build para producción
- `npm run preview` - Preview del build
- `npm run lint` - Ejecutar ESLint

## 📋 Funcionalidades Pendientes

### Implementaciones Requeridas
1. **Backend (Cloud Functions)**:
   - API endpoints para productos
   - Gestión de órdenes
   - Autenticación de administradores
   - Procesamiento de pagos con Stripe

2. **Configuración de Firebase**:
   - Reglas de seguridad de Firestore
   - Configuración de Authentication
   - Índices de base de datos

3. **Configuración de Capacitor**:
   - Plugins de Firebase para mobile
   - Configuración de permisos
   - Iconos y splash screens

4. **Funcionalidades Adicionales**:
   - Sistema de reseñas
   - Notificaciones push
   - Programa de lealtad
   - Chat de soporte

## 🚀 Despliegue

### Web
- Vercel, Netlify, o Firebase Hosting
- Configurar variables de entorno para Stripe y Firebase

### Mobile
- Google Play Store (Android)
- Apple App Store (iOS)

## 📞 Soporte

Para soporte técnico o preguntas sobre la implementación, contacta al equipo de desarrollo.

## 📄 Licencia

Este proyecto es propiedad de FuXion y está protegido por derechos de autor.