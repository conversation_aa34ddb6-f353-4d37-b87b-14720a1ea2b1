rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Products - Read access for everyone, write access for admins only
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Users - Users can read/write their own data, admins can read all
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // User Addresses - Users can only access their own addresses
    match /userAddresses/{addressId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }

    // Orders - Users can read their own orders, admins can read/write all orders
    match /orders/{orderId} {
      // Users can read their own orders
      allow read: if request.auth != null && 
        resource.data.customerId == request.auth.uid;
      
      // Users can create orders for themselves
      allow create: if request.auth != null && 
        request.resource.data.customerId == request.auth.uid;
      
      // Admins can read and write all orders
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}