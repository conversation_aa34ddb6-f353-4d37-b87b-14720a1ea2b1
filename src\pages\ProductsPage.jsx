import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Search, Filter, ArrowLeft } from 'lucide-react'
import { Card, CardContent, CardFooter } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { useCart } from '../context/CartContext'
import { apiService } from '../services/api'

const ProductsPage = () => {
  const { categoria } = useParams()
  const navigate = useNavigate()
  const { addItem } = useCart()
  
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState(categoria || 'all')

  const categories = [
    { id: 'all', name: 'Todos' },
    { id: 'digestivo', name: 'Digestivo' },
    { id: 'perdida-peso', name: '<PERSON><PERSON><PERSON><PERSON> de Peso' },
    { id: 'belleza', name: 'Belleza' },
    { id: 'energia', name: 'Energía' },
    { id: 'respiratorio', name: 'Respiratorio' },
    { id: 'bienestar', name: 'Bienestar' },
    { id: 'detox', name: 'Detox' },
    { id: 'inmunidad', name: 'Inmunidad' },
    { id: 'mental', name: 'Mental' }
  ]

  // Mock products data - replace with API call
  const mockProducts = [
    {
      id: 1,
      name: 'PRUNEX 1',
      category: 'digestivo',
      price: 45.99,
      image: '/products/prunex1.jpg',
      description: 'Ayuda natural para el estreñimiento'
    },
    {
      id: 2,
      name: 'OMEGAFIT',
      category: 'digestivo',
      price: 52.99,
      image: '/products/omegafit.jpg',
      description: 'Para gastritis y acidez'
    },
    {
      id: 3,
      name: 'THERMO T5',
      category: 'perdida-peso',
      price: 68.99,
      image: '/products/thermo-t5.jpg',
      description: 'Quemador de grasa natural'
    },
    {
      id: 4,
      name: 'BEAUTY-IN',
      category: 'belleza',
      price: 75.99,
      image: '/products/beauty-in.jpg',
      description: 'Colágeno para tu piel'
    },
    {
      id: 5,
      name: 'VITA XTRA+',
      category: 'energia',
      price: 42.99,
      image: '/products/vita-xtra.jpg',
      description: 'Energía todo el día'
    },
    {
      id: 6,
      name: 'YERBA LIFE',
      category: 'respiratorio',
      price: 38.99,
      image: '/products/yerba-life.jpg',
      description: 'Limpia tus vías respiratorias'
    }
  ]

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        // Try to get products from Firestore
        const data = await apiService.getProducts(categoria)
        setProducts(data.length > 0 ? data : mockProducts)
      } catch (error) {
        console.error('Error fetching products:', error)
        // Fallback to mock data if Firestore fails
        setProducts(mockProducts)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [categoria])

  useEffect(() => {
    let filtered = products

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredProducts(filtered)
  }, [products, selectedCategory, searchTerm])

  const handleAddToCart = (product) => {
    addItem(product)
    // You could add a toast notification here
  }

  const handleProductClick = (productId) => {
    navigate(`/producto/${productId}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando productos...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/')}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">
                Productos FuXion
              </h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Filters and Search */}
        <div className="mb-6 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Buscar productos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div 
                className="cursor-pointer"
                onClick={() => handleProductClick(product.id)}
              >
                <div className="aspect-square bg-gray-200 relative overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = `https://via.placeholder.com/300x300/e5e7eb/6b7280?text=${encodeURIComponent(product.name)}`
                    }}
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                  <p className="text-gray-600 text-sm mb-3">{product.description}</p>
                  <p className="text-2xl font-bold text-blue-600">
                    ${product.price.toFixed(2)}
                  </p>
                </CardContent>
              </div>
              <CardFooter className="p-4 pt-0">
                <Button 
                  className="w-full"
                  onClick={() => handleAddToCart(product)}
                >
                  Añadir al Carrito
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No se encontraron productos</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductsPage