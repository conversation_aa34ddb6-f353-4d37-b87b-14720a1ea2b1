import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Search, Filter, ArrowLeft } from 'lucide-react'
import { Card, CardContent, CardFooter } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { useCart } from '../context/CartContext'
import { apiService } from '../services/api'

const ProductsPage = () => {
  const { categoria } = useParams()
  const navigate = useNavigate()
  const { addItem } = useCart()
  
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState(categoria || 'all')

  const categories = [
    { id: 'all', name: 'Todos' },
    { id: 'digestivo', name: 'Digestivo' },
    { id: 'perdida-peso', name: '<PERSON><PERSON><PERSON><PERSON> de Peso' },
    { id: 'belleza', name: 'Belleza' },
    { id: 'energia', name: 'Energía' },
    { id: 'respiratorio', name: 'Respiratorio' },
    { id: 'bienestar', name: 'Bienestar' },
    { id: 'detox', name: 'Detox' },
    { id: 'inmunidad', name: 'Inmunidad' },
    { id: 'mental', name: 'Mental' }
  ]

  // Mock products data - replace with API call
  const mockProducts = [
    {
      id: 1,
      name: 'PRUNEX 1',
      category: 'digestivo',
      price: 45.99,
      image: '/products/prunex1.jpg',
      description: 'Ayuda natural para el estreñimiento'
    },
    {
      id: 2,
      name: 'OMEGAFIT',
      category: 'digestivo',
      price: 52.99,
      image: '/products/omegafit.jpg',
      description: 'Para gastritis y acidez'
    },
    {
      id: 3,
      name: 'THERMO T5',
      category: 'perdida-peso',
      price: 68.99,
      image: '/products/thermo-t5.jpg',
      description: 'Quemador de grasa natural'
    },
    {
      id: 4,
      name: 'BEAUTY-IN',
      category: 'belleza',
      price: 75.99,
      image: '/products/beauty-in.jpg',
      description: 'Colágeno para tu piel'
    },
    {
      id: 5,
      name: 'VITA XTRA+',
      category: 'energia',
      price: 42.99,
      image: '/products/vita-xtra.jpg',
      description: 'Energía todo el día'
    },
    {
      id: 6,
      name: 'YERBA LIFE',
      category: 'respiratorio',
      price: 38.99,
      image: '/products/yerba-life.jpg',
      description: 'Limpia tus vías respiratorias'
    }
  ]

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        // Try to get products from Firestore
        const data = await apiService.getProducts(categoria)
        setProducts(data.length > 0 ? data : mockProducts)
      } catch (error) {
        console.error('Error fetching products:', error)
        // Fallback to mock data if Firestore fails
        setProducts(mockProducts)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [categoria])

  useEffect(() => {
    let filtered = products

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredProducts(filtered)
  }, [products, selectedCategory, searchTerm])

  const handleAddToCart = (product) => {
    addItem(product)
    // You could add a toast notification here
  }

  const handleProductClick = (productId) => {
    navigate(`/producto/${productId}`)
  }

  if (loading) {
    return (
      <div className="fuxion-page flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6"></div>
          <p className="text-white text-xl font-bold">Cargando productos...</p>
          <p className="text-white/80 mt-2">Preparando tu experiencia FuXion</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fuxion-page">
      {/* Decorative Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>

      {/* Header */}
      <div className="fuxion-header relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/')}
                className="text-blue-600 hover:text-blue-800"
              >
                <ArrowLeft className="h-6 w-6" />
              </Button>
              <div>
                <h1 className="text-3xl font-black text-blue-600 tracking-wide">
                  PRODUCTOS FUXION
                </h1>
                <p className="text-blue-500 font-medium">Encuentra tu solución de salud ideal</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="fuxion-content relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Filters and Search */}
          <div className="mb-8 space-y-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-400 h-6 w-6" />
              <input
                placeholder="Buscar productos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="fuxion-input w-full pl-12 pr-4 py-4 text-lg"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-3">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="px-6 py-3 text-sm font-bold"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredProducts.map((product) => (
              <div key={product.id} className="product-card">
                <div
                  className="cursor-pointer"
                  onClick={() => handleProductClick(product.id)}
                >
                  <div className="aspect-square bg-gradient-to-br from-blue-50 to-blue-100 relative overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                      onError={(e) => {
                        e.target.src = `https://via.placeholder.com/300x300/e5e7eb/6b7280?text=${encodeURIComponent(product.name)}`
                      }}
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="font-black text-xl mb-2 text-gray-800 tracking-wide">{product.name}</h3>
                    <p className="text-gray-600 text-sm mb-4 leading-relaxed">{product.description}</p>
                    <p className="text-3xl font-black text-blue-600 mb-4">
                      ${product.price.toFixed(2)}
                    </p>
                    <Button
                      variant="primary"
                      className="w-full py-3 text-sm font-bold"
                      onClick={() => handleAddToCart(product)}
                    >
                      AÑADIR AL CARRITO
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-16">
              <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-8 max-w-md mx-auto">
                <p className="text-blue-600 text-xl font-bold">No se encontraron productos</p>
                <p className="text-blue-500 mt-2">Intenta con otros términos de búsqueda</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProductsPage