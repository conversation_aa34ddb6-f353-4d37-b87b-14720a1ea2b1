import { collection, addDoc, doc, setDoc } from 'firebase/firestore'
import { db } from '../firebase-config'
import { 
  sampleProducts, 
  sampleOrders, 
  sampleUsers, 
  sampleUserAddresses 
} from '../data/sampleData'

export const initializeFirestore = async () => {
  try {
    console.log('Initializing Firestore with sample data...')

    // Add products
    console.log('Adding products...')
    for (const product of sampleProducts) {
      await addDoc(collection(db, 'products'), product)
    }

    // Add users
    console.log('Adding users...')
    for (const user of sampleUsers) {
      await setDoc(doc(db, 'users', user.uid), user)
    }

    // Add orders
    console.log('Adding orders...')
    for (const order of sampleOrders) {
      await addDoc(collection(db, 'orders'), {
        ...order,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }

    // Add user addresses
    console.log('Adding user addresses...')
    for (const address of sampleUserAddresses) {
      await addDoc(collection(db, 'userAddresses'), {
        ...address,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }

    console.log('Firestore initialization completed successfully!')
    return { success: true, message: 'Database initialized with sample data' }
  } catch (error) {
    console.error('Error initializing Firestore:', error)
    return { success: false, error: error.message }
  }
}