import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { ArrowLeft, Package, User, MapPin, CreditCard, Truck, Save } from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Input } from '../../components/ui/Input'
import { apiService } from '../../services/api'

const AdminOrderDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [trackingNumber, setTrackingNumber] = useState('')

  const orderStatuses = [
    { id: 'pending', name: '<PERSON><PERSON><PERSON>', color: 'yellow' },
    { id: 'paid', name: '<PERSON><PERSON>', color: 'blue' },
    { id: 'shipped', name: '<PERSON><PERSON><PERSON>', color: 'green' },
    { id: 'delivered', name: 'Entregado', color: 'green' }
  ]

  // Mock order data - replace with API call
  const mockOrder = {
    id: 'ORD-001',
    customerName: 'Juan Pérez',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    total: 125.99,
    subtotal: 111.98,
    shipping: 0.00,
    tax: 14.01,
    status: 'paid',
    paymentMethod: 'Tarjeta de Crédito',
    paymentIntentId: 'pi_1234567890',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:35:00Z',
    trackingNumber: '',
    items: [
      {
        id: 1,
        name: 'THERMO T5',
        description: 'Quemador de grasa natural',
        quantity: 1,
        price: 68.99,
        image: '/products/thermo-t5.jpg'
      },
      {
        id: 2,
        name: 'VITA XTRA+',
        description: 'Energía todo el día',
        quantity: 1,
        price: 42.99,
        image: '/products/vita-xtra.jpg'
      }
    ],
    shippingAddress: {
      fullName: 'Juan Pérez',
      phone: '+****************',
      address: '123 Main Street, Apt 4B',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      country: 'US'
    },
    billingAddress: {
      fullName: 'Juan Pérez',
      phone: '+****************',
      address: '123 Main Street, Apt 4B',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      country: 'US'
    }
  }

  useEffect(() => {
    loadOrder()
  }, [id])

  const loadOrder = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('admin-token')
      
      if (!token) {
        navigate('/admin/login')
        return
      }

      // Try to get order from Firestore
      const orderData = await apiService.getOrder(id)
      setOrder(orderData)
      setNewStatus(orderData.status)
      setTrackingNumber(orderData.trackingNumber || '')
    } catch (error) {
      console.error('Error loading order:', error)
      // Fallback to mock data if Firestore fails
      setOrder(mockOrder)
      setNewStatus(mockOrder.status)
      setTrackingNumber(mockOrder.trackingNumber || '')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateOrder = async () => {
    try {
      setUpdating(true)
      
      await apiService.updateOrderStatus(id, newStatus, trackingNumber)
      
      // Update local state
      setOrder(prev => ({
        ...prev,
        status: newStatus,
        trackingNumber: trackingNumber,
        updatedAt: new Date().toISOString()
      }))
      
      // Show success message (you could use a toast here)
      alert('Orden actualizada exitosamente')
    } catch (error) {
      console.error('Error updating order:', error)
      alert('Error al actualizar la orden')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status) => {
    const statusConfig = orderStatuses.find(s => s.id === status)
    return statusConfig?.color || 'gray'
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando orden...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 text-lg">Orden no encontrada</p>
          <Button onClick={() => navigate('/admin/ordenes')} className="mt-4">
            Volver a órdenes
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/admin/ordenes')}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Orden {order.id}
                </h1>
                <p className="text-gray-600">
                  Creada el {formatDate(order.createdAt)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium bg-${getStatusColor(order.status)}-100 text-${getStatusColor(order.status)}-800`}>
                {orderStatuses.find(s => s.id === order.status)?.name}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Productos Ordenados
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.src = `https://via.placeholder.com/64x64/e5e7eb/6b7280?text=${encodeURIComponent(item.name.substring(0, 2))}`
                          }}
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold">{item.name}</h3>
                        <p className="text-sm text-gray-600">{item.description}</p>
                        <p className="text-sm text-gray-600">
                          Cantidad: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">${item.price.toFixed(2)}</p>
                        <p className="text-sm text-gray-600">
                          Total: ${(item.price * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Información del Cliente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Datos de Contacto</h4>
                    <p className="text-gray-600">{order.customerName}</p>
                    <p className="text-gray-600">{order.customerEmail}</p>
                    <p className="text-gray-600">{order.customerPhone}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Dirección de Envío
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-gray-600">
                  <p className="font-semibold">{order.shippingAddress.fullName}</p>
                  <p>{order.shippingAddress.phone}</p>
                  <p>{order.shippingAddress.address}</p>
                  <p>
                    {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                  </p>
                  <p>{order.shippingAddress.country}</p>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Información de Pago
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Método de Pago:</span>
                    <span className="font-semibold">{order.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ID de Transacción:</span>
                    <span className="font-mono text-sm">{order.paymentIntentId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Estado del Pago:</span>
                    <span className="text-green-600 font-semibold">Completado</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Resumen de la Orden</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${order.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Envío:</span>
                  <span>${order.shipping.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Impuestos:</span>
                  <span>${order.tax.toFixed(2)}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total:</span>
                    <span>${order.total.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Update Order Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Truck className="h-5 w-5 mr-2" />
                  Actualizar Estado
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estado de la Orden
                  </label>
                  <select
                    value={newStatus}
                    onChange={(e) => setNewStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {orderStatuses.map((status) => (
                      <option key={status.id} value={status.id}>
                        {status.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Número de Seguimiento
                  </label>
                  <Input
                    placeholder="Ej: TRK123456789"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleUpdateOrder}
                  disabled={updating}
                  className="w-full"
                >
                  {updating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Actualizando...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Actualizar Orden
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Order Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Historial de la Orden</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Orden creada</p>
                      <p className="text-xs text-gray-600">
                        {formatDate(order.createdAt)}
                      </p>
                    </div>
                  </div>
                  
                  {order.status !== 'pending' && (
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium">Pago confirmado</p>
                        <p className="text-xs text-gray-600">
                          {formatDate(order.updatedAt)}
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {order.status === 'shipped' && order.trackingNumber && (
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium">Orden enviada</p>
                        <p className="text-xs text-gray-600">
                          Tracking: {order.trackingNumber}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminOrderDetail