// Base URL for your Firebase Cloud Functions
const API_BASE_URL = 'https://your-project.cloudfunctions.net'

class ApiService {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Products
  async getProducts(category = null) {
    const endpoint = category ? `/products?category=${category}` : '/products'
    return this.request(endpoint)
  }

  async getProduct(id) {
    return this.request(`/products/${id}`)
  }

  // Orders
  async createOrder(orderData) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  }

  async getOrders(token) {
    return this.request('/admin/orders', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  async getOrder(id, token) {
    return this.request(`/admin/orders/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  async updateOrderStatus(id, status, trackingNumber, token) {
    return this.request(`/admin/orders/${id}`, {
      method: 'PATCH',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ status, trackingNumber }),
    })
  }

  // User addresses
  async getUserAddresses(token) {
    return this.request('/user/addresses', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  async saveUserAddress(addressData, token) {
    return this.request('/user/addresses', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(addressData),
    })
  }

  async updateUserAddress(id, addressData, token) {
    return this.request(`/user/addresses/${id}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(addressData),
    })
  }

  async deleteUserAddress(id, token) {
    return this.request(`/user/addresses/${id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  // Payment
  async createPaymentIntent(amount, currency = 'usd') {
    return this.request('/create-payment-intent', {
      method: 'POST',
      body: JSON.stringify({ amount, currency }),
    })
  }

  // Admin authentication
  async adminLogin(credentials) {
    return this.request('/admin/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  }
}

export const apiService = new ApiService()