import React from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ShoppingCart, User, LogOut, Menu } from 'lucide-react'
import { Button } from './ui/Button'
import { useCart } from '../context/CartContext'
import { useAuth } from '../context/AuthContext'

const Navigation = () => {
  const navigate = useNavigate()
  const { getCartItemsCount } = useCart()
  const { user, signOut, isAuthenticated } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const cartItemsCount = getCartItemsCount()

  return (
    <nav className="fuxion-nav sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* <PERSON>u <PERSON>er (Mobile) - Left */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-blue-600 hover:text-blue-800"
            >
              <Menu className="h-6 w-6" />
            </Button>
          </div>

          {/* Logo - Center */}
          <Link to="/" className="flex items-center space-x-2 absolute left-1/2 transform -translate-x-1/2 md:relative md:left-auto md:transform-none">
            <img
              src="/fuxion-logo.png"
              alt="FuXion"
              className="h-10 md:h-8"
              onError={(e) => {
                e.target.style.display = 'none'
                e.target.nextSibling.style.display = 'block'
              }}
            />
            <span className="hidden font-bold text-xl text-blue-600">FuXion</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link
              to="/productos"
              className="text-blue-600 hover:text-blue-800 font-semibold transition-colors"
            >
              Productos
            </Link>
            <Link
              to="/setup"
              className="text-blue-600 hover:text-blue-800 font-semibold transition-colors"
            >
              Configuración
            </Link>

            {/* User Menu */}
            {isAuthenticated ? (
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/mi-perfil')}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <User className="h-5 w-5 mr-1" />
                  {user?.displayName || 'Mi Perfil'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/login')}
                className="text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white"
              >
                Iniciar Sesión
              </Button>
            )}
          </div>

          {/* Cart - Right */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/carrito')}
              className="relative text-blue-600 hover:text-blue-800"
            >
              <ShoppingCart className="h-6 w-6" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                  {cartItemsCount}
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t bg-white/95 backdrop-filter backdrop-blur-lg py-4">
            <div className="space-y-2">
              <Link
                to="/productos"
                className="block px-4 py-3 text-blue-600 hover:bg-blue-50 rounded-lg font-semibold transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Productos
              </Link>
              <Link
                to="/setup"
                className="block px-4 py-3 text-blue-600 hover:bg-blue-50 rounded-lg font-semibold transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Configuración
              </Link>

              {isAuthenticated ? (
                <>
                  <Link
                    to="/mi-perfil"
                    className="block px-4 py-3 text-blue-600 hover:bg-blue-50 rounded-lg font-semibold transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Mi Perfil
                  </Link>
                  <button
                    onClick={() => {
                      handleSignOut()
                      setIsMenuOpen(false)
                    }}
                    className="block w-full text-left px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg font-semibold transition-colors"
                  >
                    Cerrar Sesión
                  </button>
                </>
              ) : (
                <Link
                  to="/login"
                  className="block px-4 py-3 text-blue-600 hover:bg-blue-50 rounded-lg font-semibold transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Iniciar Sesión
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navigation