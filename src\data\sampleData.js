// Sample data for Firestore collections
export const sampleProducts = [
  {
    name: 'PRUNEX 1',
    category: 'digestivo',
    price: 45.99,
    image: '/products/prunex1.jpg',
    images: ['/products/prunex1.jpg', '/products/prunex1-2.jpg'],
    description: 'Ayuda natural para el estreñimiento',
    longDescription: `PRUNEX 1 es un suplemento natural formulado específicamente para ayudar con problemas de estreñimiento. 
    
    Contiene ingredientes naturales que ayudan a regular el tránsito intestinal de manera suave y efectiva, sin causar dependencia.`,
    usage: `Modo de uso:
    - Tomar 1 cápsula antes de dormir
    - Acompañar con abundante agua
    - No exceder la dosis recomendada`,
    ingredients: [
      'Extracto de Ciruela Pasa',
      'Fibra Natural',
      'Extracto de Sen',
      'Magnesio',
      'Probióticos'
    ],
    benefits: [
      'Regula el tránsito intestinal',
      'Mejora la digestión',
      'Reduce la hinchazón',
      'Ayuda a la flora intestinal'
    ],
    rating: 4.3,
    reviewsCount: 89,
    inStock: true,
    stock: 25
  },
  {
    name: 'OMEGAFIT',
    category: 'digestivo',
    price: 52.99,
    image: '/products/omegafit.jpg',
    images: ['/products/omegafit.jpg', '/products/omegafit-2.jpg'],
    description: 'Para gastritis y acidez',
    longDescription: `OMEGAFIT es un suplemento diseñado para combatir la gastritis y la acidez estomacal de manera natural.
    
    Su fórmula única ayuda a proteger la mucosa gástrica y reducir la producción excesiva de ácido estomacal.`,
    usage: `Modo de uso:
    - Tomar 1 cápsula 30 minutos antes de cada comida principal
    - Máximo 3 cápsulas por día
    - Tomar con agua tibia`,
    ingredients: [
      'Omega 3',
      'Extracto de Manzanilla',
      'Aloe Vera',
      'Zinc',
      'Vitamina E'
    ],
    benefits: [
      'Reduce la acidez estomacal',
      'Protege la mucosa gástrica',
      'Mejora la digestión',
      'Reduce la inflamación'
    ],
    rating: 4.5,
    reviewsCount: 156,
    inStock: true,
    stock: 18
  },
  {
    name: 'THERMO T5',
    category: 'perdida-peso',
    price: 68.99,
    image: '/products/thermo-t5.jpg',
    images: ['/products/thermo-t5.jpg', '/products/thermo-t5-2.jpg', '/products/thermo-t5-3.jpg'],
    description: 'Quemador de grasa natural',
    longDescription: `THERMO T5 es un suplemento natural diseñado para acelerar el metabolismo y ayudar en la quema de grasa de manera efectiva y segura.
    
    Este producto ha sido formulado con extractos de plantas y compuestos naturales que trabajan en sinergia para proporcionar un impulso metabólico.`,
    usage: `Modo de uso:
    - Tomar 1 cápsula 30 minutos antes del desayuno
    - Tomar 1 cápsula 30 minutos antes del almuerzo
    - No exceder 2 cápsulas por día
    - No tomar después de las 6 PM`,
    ingredients: [
      'Extracto de Té Verde',
      'L-Carnitina',
      'Cafeína Natural',
      'Extracto de Garcinia Cambogia',
      'Cromo Picolinato',
      'Extracto de Guaraná'
    ],
    benefits: [
      'Acelera el metabolismo',
      'Quema grasa corporal',
      'Aumenta la energía',
      'Reduce el apetito',
      'Mejora el rendimiento físico'
    ],
    rating: 4.7,
    reviewsCount: 234,
    inStock: true,
    stock: 12
  },
  {
    name: 'BEAUTY-IN',
    category: 'belleza',
    price: 75.99,
    image: '/products/beauty-in.jpg',
    images: ['/products/beauty-in.jpg', '/products/beauty-in-2.jpg'],
    description: 'Colágeno para tu piel',
    longDescription: `BEAUTY-IN es un suplemento de colágeno hidrolizado diseñado para mejorar la salud y apariencia de tu piel desde adentro.
    
    Contiene colágeno tipo I y III, junto con vitaminas y minerales esenciales para la producción natural de colágeno.`,
    usage: `Modo de uso:
    - Mezclar 1 sobre en 250ml de agua fría
    - Tomar 1 vez al día, preferiblemente en ayunas
    - Revolver bien antes de consumir`,
    ingredients: [
      'Colágeno Hidrolizado',
      'Ácido Hialurónico',
      'Vitamina C',
      'Biotina',
      'Zinc',
      'Extracto de Acai'
    ],
    benefits: [
      'Mejora la elasticidad de la piel',
      'Reduce arrugas y líneas de expresión',
      'Fortalece uñas y cabello',
      'Hidrata la piel',
      'Antioxidante natural'
    ],
    rating: 4.6,
    reviewsCount: 178,
    inStock: true,
    stock: 20
  },
  {
    name: 'VITA XTRA+',
    category: 'energia',
    price: 42.99,
    image: '/products/vita-xtra.jpg',
    images: ['/products/vita-xtra.jpg', '/products/vita-xtra-2.jpg'],
    description: 'Energía todo el día',
    longDescription: `VITA XTRA+ es un complejo vitamínico diseñado para proporcionar energía sostenida durante todo el día.
    
    Combina vitaminas del complejo B, minerales esenciales y extractos naturales para combatir la fatiga y mejorar el rendimiento físico y mental.`,
    usage: `Modo de uso:
    - Tomar 1 cápsula con el desayuno
    - Acompañar con abundante agua
    - No tomar en la noche`,
    ingredients: [
      'Complejo B',
      'Vitamina C',
      'Hierro',
      'Magnesio',
      'Extracto de Ginseng',
      'Coenzima Q10'
    ],
    benefits: [
      'Aumenta los niveles de energía',
      'Reduce la fatiga',
      'Mejora la concentración',
      'Fortalece el sistema inmune',
      'Mejora el rendimiento físico'
    ],
    rating: 4.4,
    reviewsCount: 145,
    inStock: true,
    stock: 30
  },
  {
    name: 'YERBA LIFE',
    category: 'respiratorio',
    price: 38.99,
    image: '/products/yerba-life.jpg',
    images: ['/products/yerba-life.jpg', '/products/yerba-life-2.jpg'],
    description: 'Limpia tus vías respiratorias',
    longDescription: `YERBA LIFE es un suplemento natural formulado con hierbas tradicionales para ayudar a limpiar y descongestionar las vías respiratorias.
    
    Ideal para personas que sufren de congestión nasal, tos o problemas respiratorios menores.`,
    usage: `Modo de uso:
    - Tomar 1 cápsula 3 veces al día
    - Preferiblemente con las comidas
    - Acompañar con líquidos calientes`,
    ingredients: [
      'Eucalipto',
      'Menta',
      'Tomillo',
      'Propóleo',
      'Vitamina C',
      'Zinc'
    ],
    benefits: [
      'Descongestiona las vías respiratorias',
      'Alivia la tos',
      'Fortalece el sistema respiratorio',
      'Propiedades expectorantes',
      'Efecto antibacterial natural'
    ],
    rating: 4.2,
    reviewsCount: 98,
    inStock: true,
    stock: 22
  }
]

export const sampleOrders = [
  {
    customerName: 'Juan Pérez',
    customerEmail: '<EMAIL>',
    customerId: 'user123',
    total: 125.99,
    subtotal: 111.98,
    shipping: 0.00,
    tax: 14.01,
    status: 'paid',
    paymentMethod: 'Tarjeta de Crédito',
    paymentIntentId: 'pi_1234567890',
    trackingNumber: '',
    items: [
      {
        productId: '1',
        name: 'THERMO T5',
        price: 68.99,
        quantity: 1
      },
      {
        productId: '2',
        name: 'VITA XTRA+',
        price: 42.99,
        quantity: 1
      }
    ],
    shippingAddress: {
      fullName: 'Juan Pérez',
      phone: '+****************',
      address: '123 Main Street, Apt 4B',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      country: 'US'
    }
  },
  {
    customerName: 'María García',
    customerEmail: '<EMAIL>',
    customerId: 'user456',
    total: 75.99,
    subtotal: 75.99,
    shipping: 0.00,
    tax: 0.00,
    status: 'shipped',
    paymentMethod: 'Tarjeta de Crédito',
    paymentIntentId: 'pi_0987654321',
    trackingNumber: 'TRK123456789',
    items: [
      {
        productId: '4',
        name: 'BEAUTY-IN',
        price: 75.99,
        quantity: 1
      }
    ],
    shippingAddress: {
      fullName: 'María García',
      phone: '+****************',
      address: '456 Oak Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      country: 'US'
    }
  }
]

export const sampleUsers = [
  {
    uid: 'user123',
    email: '<EMAIL>',
    displayName: 'Juan Pérez',
    photoURL: null,
    isAdmin: false
  },
  {
    uid: 'user456',
    email: '<EMAIL>',
    displayName: 'María García',
    photoURL: null,
    isAdmin: false
  },
  {
    uid: 'admin-user',
    email: '<EMAIL>',
    displayName: 'Administrador FuXion',
    photoURL: null,
    isAdmin: true
  }
]

export const sampleUserAddresses = [
  {
    userId: 'user123',
    fullName: 'Juan Pérez',
    phone: '+****************',
    address: '123 Main Street, Apt 4B',
    city: 'Miami',
    state: 'FL',
    zipCode: '33101',
    country: 'US',
    isDefault: true
  },
  {
    userId: 'user456',
    fullName: 'María García',
    phone: '+****************',
    address: '456 Oak Avenue',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90210',
    country: 'US',
    isDefault: true
  }
]